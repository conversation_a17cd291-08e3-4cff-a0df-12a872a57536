# MySQL Database Configuration (Main Application)
spring.jpa.hibernate.ddl-auto=update
spring.datasource.url=**********************************
spring.datasource.username=root
spring.datasource.password=
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Hibernate Properties
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
debug=true

# Server Configuration
server.port=9090
spring.application.name=rental

# Stripe Configuration
stripe.api.secret-key=sk_test_51QFrt0GBPHHSXwUhggJodgK4a9MwfPSoFB1PUDRH2mbo3FdcdSQGNeTDTxlHdlWI9MZSh8oUrKUUltuIsNrgF3vR00otxW8wPB
stripe.api.publishable-key=pk_test_51QFrt0GBPHHSXwUh7T0qS4TNcmv4x087EQEd5cPfKMvDYakYtoj1ordIlK1CgiQnkS2PfHl7jLYAcCHCHPvboS0400Tx02Tn1m

# PayPal Configuration
paypal.client.id=ASmtwKnlW_8cG9U4viONcNv-RKhqah0H1Gy18Hqo9fwVLKjE8f_yRfV1jR0sUnAw5fD4d7CsamqbEXo6
paypal.client.secret=EFkaxSkpMGA0ZrdTna1ao733susDkerusrkyCFYjAxSAsGNdYoveOFhA_7NNxRYxmu0BH_E0rGKoRQ5e

# JWT Configuration
security.jwt.secret-key=XK+1eTBW8EqvLQF5xgX9vQzSbAxB6lP1aOeY3oJ9ERo
security.jwt.expiration-time=3600000
